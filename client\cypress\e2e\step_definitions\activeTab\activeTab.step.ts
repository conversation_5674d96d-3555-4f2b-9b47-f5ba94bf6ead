import { DataTable, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';

When('The user clicks on the {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

When('The user selects the {string} named {string}', (type: 'event' | 'file', eventName: string) => {
  mainPage.selectItem(eventName, type);
});

Then('The following {string} details should be visible:', (type: 'event' | 'file', dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ Field: string; 'Expected Value': string }>;
  mainPage.verifyDetails(rows, type);
});

Then('{string} Time has format: {string}', (type: 'event' | 'file', format: string) => {
  mainPage.verifyTimeFormat(format, type);
});

When('The user clicks the {string} name located on the right side', (type: 'event' | 'file') => {
  mainPage.clickDetailNameEdit(type);
});

When('The user changes {string} name to {string}', (type: 'event' | 'file', newName: string) => {
  mainPage.changeDetailName(newName, type);
});

Then('The name of the {string} should be updated to {string}', (type: 'event' | 'file', newName: string) => {
  mainPage.verifyDetailNameChanged(newName, type);
});

When('The user clicks on the {string} column header for {string}', (columnName: string, type: 'event' | 'file') => {
  mainPage.clickColumnHeader(columnName, type);
});

Then('The {string} table should be sorted by {string} in {string} order', (type: 'event' | 'file', columnName: string, sortedBy: 'a-z' | 'z-a') => {
  mainPage.clickColumnHeaderUntilSorted(columnName, sortedBy, type);

  // Wait for the API response to complete
  if (type === 'file') {
    cy.awaitNetworkResponseCode({ alias: '@fetchFiles', code: 200 });
  } else {
    cy.awaitNetworkResponseCode({ alias: '@fetchEvent', code: 200 });
  }

  mainPage.verifyColumnSortState(columnName, sortedBy, type);
});
